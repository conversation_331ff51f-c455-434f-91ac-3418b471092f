import { hasuraQuery } from "@/utils/db";
import { NextRequest, NextResponse } from "next/server";

interface SupportFAQ {
  id: number;
  question: string;
  answer: string;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

interface SupportFAQResponse {
  support_faq: SupportFAQ[];
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get("active") !== "false";

    let whereClause = "";

    if (activeOnly) {
      whereClause = "where: {is_active: {_eq: true}}";
    }

    const query = `
      query GetSupportFAQ {
        support_faq(
          ${whereClause}
          order_by: [
            {display_order: asc},
            {created_at: asc}
          ]
        ) {
          id
          question
          answer
          is_active
          display_order
          created_at
          updated_at
        }
      }
    `;

    const result = await hasuraQuery<SupportFAQResponse>(query);

    return NextResponse.json({
      success: true,
      data: result.support_faq,
      count: result.support_faq.length,
    });
  } catch (error) {
    console.error("Eroare la obținerea FAQ-urilor de suport:", error);

    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare la încărcarea întrebărilor frecvente.",
        code: "FETCH_ERROR",
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}
