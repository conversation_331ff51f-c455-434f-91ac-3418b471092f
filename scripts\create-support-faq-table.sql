CREATE TABLE IF NOT EXISTS support_faq (
    id SERIAL PRIMARY KEY,
    question VARCHAR(500) NOT NULL,
    answer TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TRIGGER update_support_faq_updated_at
    BEFORE UPDATE ON support_faq
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

INSERT INTO support_faq (question, answer, display_order) VALUES
(
    'Cum mă ajută această aplicație să îmi gestionez investițiile?',
    'Aplicația Portavio vă oferă o platformă centralizată pentru monitorizarea și gestionarea portofoliului de investiții. Prin conectarea automată cu brokerul dumneavoastră, puteți urmări în timp real performanța investițiilor, analiza distribuția activelor și lua decizii informate. Platforma oferă grafice interactive, rapoarte detaliate și alerte personalizate pentru a vă ține la curent cu evoluția investițiilor.',
    1
),
(
    'Sunt datele mele financiare în siguranță?',
    'Da! Utilizăm criptare la nivel bancar și protocoale de securitate standard din industrie pentru a vă proteja datele. Informațiile dumneavoastră personale și financiare nu sunt niciodată partajate cu terțe părți, iar dumneavoastră aveți control complet asupra conturilor conectate și setărilor de confidențialitate. Toate datele sunt stocate în servere securizate și sunt supuse unor verificări regulate de securitate.',
    2
),
(
    'Oferă aplicația sfaturi de investiții sau recomandări?',
    'Nu, aplicația Portavio nu oferă sfaturi de investiții sau recomandări financiare. Suntem o platformă de monitorizare și analiză a portofoliului care vă ajută să vizualizați și să înțelegeți mai bine investițiile existente. Pentru sfaturi de investiții, vă recomandăm să consultați un consilier financiar autorizat. Platforma noastră vă oferă instrumentele necesare pentru a analiza datele, dar deciziile de investiții rămân în totalitate ale dumneavoastră.',
    3
);
