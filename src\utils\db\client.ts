import {
  GraphQLResponse,
  GraphQLRequest,
  GraphQLClientConfig,
  GraphQLClientError,
  GraphQLNetworkError,
  GraphQLValidationError,
  Variables,
  QueryOptions,
  MutationOptions,
} from "./types";

/**
 * GraphQL Client for Hasura
 *
 * A fully typed GraphQL client using plain fetch with comprehensive error handling
 * and support for queries, mutations, and subscriptions.
 */
export class GraphQLClient {
  private config: GraphQLClientConfig;

  constructor(config: GraphQLClientConfig) {
    this.config = {
      timeout: 30000, // 30 seconds default timeout
      ...config,
    };
  }

  /**
   * Execute a GraphQL query
   */
  async query<TData = any, TVariables extends Variables = Variables>(
    query: string,
    options?: QueryOptions<TVariables>
  ): Promise<TData> {
    return this.request<TData, TVariables>(
      {
        query,
        variables: options?.variables,
        operationName: options?.operationName,
      },
      options
    );
  }

  /**
   * Execute a GraphQL mutation
   */
  async mutate<TData = any, TVariables extends Variables = Variables>(
    mutation: string,
    options?: MutationOptions<TVariables>
  ): Promise<TData> {
    return this.request<TData, TVariables>(
      {
        query: mutation,
        variables: options?.variables,
        operationName: options?.operationName,
      },
      options
    );
  }

  /**
   * Execute a raw GraphQL request
   */
  async request<TData = any, TVariables extends Variables = Variables>(
    request: GraphQLRequest,
    options?: QueryOptions<TVariables>
  ): Promise<TData> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, options?.timeout || this.config.timeout);

    try {
      const headers = {
        "Content-Type": "application/json",
        ...this.config.headers,
        ...options?.headers,
      };

      const response = await fetch(this.config.endpoint, {
        method: "POST",
        headers,
        body: JSON.stringify(request),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new GraphQLNetworkError(
          `HTTP ${response.status}: ${response.statusText}`,
          { response, request }
        );
      }

      const result: GraphQLResponse<TData> = await response.json();

      // Handle GraphQL errors
      if (result.errors && result.errors.length > 0) {
        throw new GraphQLValidationError(
          `GraphQL errors: ${result.errors.map((e) => e.message).join(", ")}`,
          result.errors,
          { request }
        );
      }

      // Handle missing data
      if (result.data === undefined) {
        throw new GraphQLClientError("No data returned from GraphQL query", {
          response,
          request,
        });
      }

      return result.data;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof GraphQLClientError) {
        throw error;
      }

      if (error instanceof TypeError && error.message.includes("fetch")) {
        throw new GraphQLNetworkError(
          "Network error: Unable to connect to GraphQL endpoint",
          { request }
        );
      }

      if (error instanceof Error && error.name === "AbortError") {
        throw new GraphQLNetworkError(
          `Request timeout after ${options?.timeout || this.config.timeout}ms`,
          { request }
        );
      }

      throw new GraphQLClientError(
        error instanceof Error ? error.message : "Unknown error occurred",
        { request }
      );
    }
  }

  /**
   * Update client configuration
   */
  setConfig(config: Partial<GraphQLClientConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  getConfig(): GraphQLClientConfig {
    return { ...this.config };
  }
}
