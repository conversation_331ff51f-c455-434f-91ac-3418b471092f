"use client";

import logo from "@/app/assets/portavio-logo.svg";
import { ThemeToggle, ThemeToggleMobile } from "@/components/theme-toggle";
import { Menu, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface HeaderProps {
  variant?: "landing" | "page";
}

export function Header({ variant = "page" }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isLanding = variant === "landing";

  return (
    <header
      className={`
        fixed top-0 left-0 right-0 z-50 flex justify-between items-center px-4 md:px-8 lg:px-36 py-2
        ${
          isLanding
            ? "bg-background/0 backdrop-blur-xs"
            : "shadow-lg backdrop-blur-lg bg-white/20 dark:bg-black/20"
        }
      `}
      style={
        !isLanding
          ? {
              backgroundImage: "url('/header-bg.svg')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }
          : undefined
      }
    >
      <div className="flex items-center gap-2">
        <Link href={"/"}>
          <Image
            src={logo}
            alt="Portavio Logo"
            height={64}
            className="h-12 md:h-16 w-auto"
          />
        </Link>
      </div>

      <nav className="hidden md:flex items-center gap-6 text-sm font-medium">
        <Link
          href="/"
          className="text-white hover:text-blue-200 transition-colors"
        >
          Loghează-te
        </Link>
        <Link
          href="/"
          className="text-white hover:text-blue-200 transition-colors"
        >
          Fă primul pas
        </Link>

        <ThemeToggle />
      </nav>

      <div className="md:hidden flex items-center gap-2">
        <button
          className="md:hidden text-white p-2"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {isMobileMenuOpen && (
        <div className="absolute top-full left-0 right-0 shadow-2xl bg-[#1A1A19] backdrop-blur-sm border-t border-gray-700/50 md:hidden">
          <nav className="flex flex-col p-4 gap-4">
            <Link
              href="/"
              className="text-white hover:text-blue-200 transition-colors py-2 text-center"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Loghează-te
            </Link>
            <Link
              href="/"
              className="text-white hover:text-blue-200 transition-colors py-2 text-center"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Fă primul pas
            </Link>
            <div className="pt-2 border-t border-border/50">
              <ThemeToggleMobile />
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}
