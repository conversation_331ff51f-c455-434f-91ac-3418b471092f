import { GraphQLClient } from "./client";
import { Variables, QueryOptions, MutationOptions } from "./types";

/**
 * Hasura GraphQL Client Configuration
 */
const getHasuraConfig = () => {
  const endpoint = process.env.HASURA_ENDPOINT;
  const secret = process.env.HASURA_SECRET;

  if (!endpoint) {
    throw new Error("HASURA_ENDPOINT environment variable is required");
  }

  if (!secret) {
    throw new Error("HASURA_SECRET environment variable is required");
  }

  return {
    endpoint,
    headers: {
      "x-hasura-admin-secret": secret,
    },
  };
};

/**
 * Hasura GraphQL Client Instance
 */
export const hasuraClient = new GraphQLClient(getHasuraConfig());

/**
 * Convenience functions for Hasura operations
 */

/**
 * Execute a GraphQL query against Hasura
 */
export async function hasuraQuery<
  TData = any,
  TVariables extends Variables = Variables
>(query: string, options?: QueryOptions<TVariables>): Promise<TData> {
  return hasuraClient.query<TData, TVariables>(query, options);
}

/**
 * Execute a GraphQL mutation against Hasura
 */
export async function hasuraMutation<
  TData = any,
  TVariables extends Variables = Variables
>(mutation: string, options?: MutationOptions<TVariables>): Promise<TData> {
  return hasuraClient.mutate<TData, TVariables>(mutation, options);
}

/**
 * Hasura-specific query builders and helpers
 */

/**
 * Build a simple select query for a table
 */
export function buildSelectQuery(
  table: string,
  fields: string[],
  where?: Record<string, any>,
  orderBy?: Record<string, "asc" | "desc">,
  limit?: number,
  offset?: number
): string {
  let query = `query {
    ${table}`;

  // Add parameters
  const params: string[] = [];

  if (where) {
    params.push(`where: ${JSON.stringify(where)}`);
  }

  if (orderBy) {
    const orderByArray = Object.entries(orderBy).map(([field, direction]) => ({
      [field]: direction,
    }));
    params.push(`order_by: ${JSON.stringify(orderByArray)}`);
  }

  if (limit) {
    params.push(`limit: ${limit}`);
  }

  if (offset) {
    params.push(`offset: ${offset}`);
  }

  if (params.length > 0) {
    query += `(${params.join(", ")})`;
  }

  query += ` {
    ${fields.join("\n    ")}
  }
}`;

  return query;
}

/**
 * Build an insert mutation for a table
 */
export function buildInsertMutation(
  table: string,
  fields: string[],
  onConflict?: {
    constraint: string;
    update_columns: string[];
  }
): string {
  let mutation = `mutation Insert${table}($objects: [${table}_insert_input!]!) {
    insert_${table}`;

  const params = ["objects: $objects"];

  if (onConflict) {
    params.push(`on_conflict: {
      constraint: ${onConflict.constraint}
      update_columns: [${onConflict.update_columns.join(", ")}]
    }`);
  }

  mutation += `(${params.join(", ")}) {
    affected_rows
    returning {
      ${fields.join("\n      ")}
    }
  }
}`;

  return mutation;
}

/**
 * Build an update mutation for a table
 */
export function buildUpdateMutation(
  table: string,
  fields: string[],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  pkColumns: string[] = ["id"]
): string {
  return `mutation Update${table}($pk_columns: ${table}_pk_columns_input!, $set: ${table}_set_input!) {
    update_${table}_by_pk(pk_columns: $pk_columns, _set: $set) {
      ${fields.join("\n      ")}
    }
  }`;
}

/**
 * Build a delete mutation for a table
 */
export function buildDeleteMutation(
  table: string,
  fields: string[] = ["id"]
): string {
  return `mutation Delete${table}($where: ${table}_bool_exp!) {
    delete_${table}(where: $where) {
      affected_rows
      returning {
        ${fields.join("\n        ")}
      }
    }
  }`;
}

/**
 * Error handling utilities for Hasura-specific errors
 */
export function isHasuraPermissionError(error: any): boolean {
  return error?.graphQLErrors?.some(
    (e: any) =>
      e.extensions?.code === "access-denied" ||
      e.message?.includes("permission")
  );
}

export function isHasuraConstraintError(error: any): boolean {
  return error?.graphQLErrors?.some(
    (e: any) =>
      e.extensions?.code === "constraint-violation" ||
      e.message?.includes("constraint")
  );
}

export function isHasuraValidationError(error: any): boolean {
  return error?.graphQLErrors?.some(
    (e: any) =>
      e.extensions?.code === "validation-failed" ||
      e.message?.includes("validation")
  );
}
