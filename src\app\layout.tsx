import { PortavioLayout } from "@/components/portavio-layout";
import { ThemeProvider } from "@/components/theme-provider";
import type { Metadata } from "next";
import { Instrument_Sans } from "next/font/google";
import "./globals.css";
import Scroll from "@/components/scroll";

const instrumentSans = Instrument_Sans({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Portavio",
  description:
    "Conectează-te automat cu brokerul tău și urmărește-ți portofoliul în detaliu.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={instrumentSans.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange={false}
        >
          <Scroll />
          <PortavioLayout>{children}</PortavioLayout>
        </ThemeProvider>
      </body>
    </html>
  );
}
