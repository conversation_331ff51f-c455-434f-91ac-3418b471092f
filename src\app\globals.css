@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  /* === PORTAVIO BRAND COLORS === */

  /* Primary Brand Colors */
  --color-portavio-orange: #ff7f50;
  --color-portavio-orange-hover: #ff6450;
  --color-portavio-orange-light: #ff9570;
  --color-portavio-orange-dark: #e55a30;

  --color-portavio-blue: #5ad4ff;
  --color-portavio-blue-hover: #4ac3ee;
  --color-portavio-blue-light: #7de0ff;
  --color-portavio-blue-dark: #3bb8dd;

  --color-portavio-navy: #042d5f;
  --color-portavio-navy-light: #0a4080;
  --color-portavio-navy-dark: #021a3a;

  /* === LIGHT THEME COLORS === */

  /* Backgrounds */
  --color-background: #ffffff;
  --color-background-secondary: #f8f9fa;
  --color-background-tertiary: #f5f5f5;
  --color-background-muted: #f1f3f4;

  /* Surface/Card Colors */
  --color-surface: #ffffff;
  --color-surface-elevated: #ffffff;
  --color-surface-hover: #f8f9fa;
  --color-surface-pressed: #f1f3f4;

  /* Text Colors */
  --color-text-primary: #1a1a1a;
  --color-text-secondary: #4a5568;
  --color-text-tertiary: #718096;
  --color-text-muted: #a0aec0;
  --color-text-inverse: #ffffff;

  /* Border Colors */
  --color-border: #e2e8f0;
  --color-border-light: #f1f5f9;
  --color-border-strong: #cbd5e0;
  --color-border-inverse: #4a5568;

  /* State Colors */
  --color-success: #10b981;
  --color-success-light: #34d399;
  --color-success-dark: #059669;
  --color-success-bg: #ecfdf5;

  --color-warning: #f59e0b;
  --color-warning-light: #fbbf24;
  --color-warning-dark: #d97706;
  --color-warning-bg: #fffbeb;

  --color-error: #ef4444;
  --color-error-light: #f87171;
  --color-error-dark: #dc2626;
  --color-error-bg: #fef2f2;

  --color-info: #3b82f6;
  --color-info-light: #60a5fa;
  --color-info-dark: #2563eb;
  --color-info-bg: #eff6ff;

  /* Interactive Colors */
  --color-interactive: #5ad4ff;
  --color-interactive-hover: #4ac3ee;
  --color-interactive-pressed: #3bb8dd;
  --color-interactive-disabled: #e2e8f0;

  /* Chart/Data Visualization Colors */
  --color-chart-1: #ff7f50;
  --color-chart-2: #5ad4ff;
  --color-chart-3: #10b981;
  --color-chart-4: #f59e0b;
  --color-chart-5: #8b5cf6;
  --color-chart-6: #ef4444;
  --color-chart-7: #06b6d4;
  --color-chart-8: #84cc16;

  /* === DARK THEME COLORS === */

  /* Dark Backgrounds */
  --color-background-dark: #1a1a19;
  --color-background-secondary-dark: #2a2d35;
  --color-background-tertiary-dark: #3e4047;
  --color-background-muted-dark: #4a4d55;

  /* Dark Surface/Card Colors */
  --color-surface-dark: #2a2d35;
  --color-surface-elevated-dark: #3e4047;
  --color-surface-hover-dark: #4a4d55;
  --color-surface-pressed-dark: #5a5d65;

  /* Dark Text Colors */
  --color-text-primary-dark: #ffffff;
  --color-text-secondary-dark: #e2e8f0;
  --color-text-tertiary-dark: #cbd5e0;
  --color-text-muted-dark: #94a3b8;
  --color-text-inverse-dark: #1a1a1a;

  /* Dark Border Colors */
  --color-border-dark: #4a5568;
  --color-border-light-dark: #3e4047;
  --color-border-strong-dark: #718096;
  --color-border-inverse-dark: #e2e8f0;

  /* Dark State Colors */
  --color-success-dark-theme: #22c55e;
  --color-success-light-dark: #4ade80;
  --color-success-dark-dark: #16a34a;
  --color-success-bg-dark: #052e16;

  --color-warning-dark-theme: #eab308;
  --color-warning-light-dark: #facc15;
  --color-warning-dark-dark: #ca8a04;
  --color-warning-bg-dark: #1e1b00;

  --color-error-dark-theme: #f87171;
  --color-error-light-dark: #fca5a5;
  --color-error-dark-dark: #dc2626;
  --color-error-bg-dark: #2d1b1b;

  --color-info-dark-theme: #60a5fa;
  --color-info-light-dark: #93c5fd;
  --color-info-dark-dark: #3b82f6;
  --color-info-bg-dark: #1e293b;

  /* Dark Interactive Colors */
  --color-interactive-dark: #5ad4ff;
  --color-interactive-hover-dark: #7de0ff;
  --color-interactive-pressed-dark: #3bb8dd;
  --color-interactive-disabled-dark: #4a5568;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* Light theme colors */
  --background: #ffffff;
  --foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --primary: var(--color-portavio-orange); /* Portavio Orange */
  --primary-foreground: #ffffff;
  --secondary: #f8f9fa;
  --secondary-foreground: #1a1a1a;
  --muted: #f5f5f5;
  --muted-foreground: #718096;
  --accent: #5ad4ff; /* Portavio Blue */
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #5ad4ff;

  /* Chart colors - Portavio brand palette */
  --chart-1: #ff7f50; /* Portavio Orange */
  --chart-2: #5ad4ff; /* Portavio Blue */
  --chart-3: #10b981; /* Success Green */
  --chart-4: #f59e0b; /* Warning Yellow */
  --chart-5: #8b5cf6; /* Purple */

  /* Sidebar colors */
  --sidebar: #ffffff;
  --sidebar-foreground: #718096;
  --sidebar-primary: #ff7f50; /* Portavio Orange */
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f8f9fa;
  --sidebar-accent-foreground: #1a1a1a;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #5ad4ff;
}

.dark {
  /* Dark theme colors */
  --background: #1a1a19; /* Portavio Dark BG */
  --foreground: #ffffff;
  --card: #3e4047; /* Portavio Section BG */
  --card-foreground: #ffffff;
  --popover: #2a2d35;
  --popover-foreground: #ffffff;
  --primary: var(--color-portavio-orange); /* Portavio Orange */
  --primary-foreground: #ffffff;
  --secondary: #3e4047; /* Portavio Section BG */
  --secondary-foreground: #ffffff;
  --muted: #4a4d55;
  --muted-foreground: #94a3b8;
  --accent: #5ad4ff; /* Portavio Blue */
  --accent-foreground: #ffffff;
  --destructive: #f87171;
  --border: #4a5568;
  --input: #4a5568;
  --ring: #5ad4ff;

  /* Dark theme chart colors - same as light theme */
  --chart-1: #ff7f50; /* Portavio Orange */
  --chart-2: #5ad4ff; /* Portavio Blue */
  --chart-3: #22c55e; /* Success Green (darker) */
  --chart-4: #eab308; /* Warning Yellow (darker) */
  --chart-5: #a855f7; /* Purple (darker) */

  /* Dark theme sidebar colors */
  --sidebar: #2a2d35;
  --sidebar-foreground: #e2e8f0;
  --sidebar-primary: #ff7f50;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #3e4047;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #4a5568;
  --sidebar-ring: #5ad4ff;

  /* Portavio brand colors remain the same in dark mode */
  --portavio-orange: #ff7f50;
  --portavio-orange-hover: #ff6450;
  --portavio-blue: #5ad4ff;
  --portavio-blue-hover: #4ac3ee;
  --portavio-navy: #042d5f;
  --portavio-dark-bg: #1a1a19;
  --portavio-section-bg: #3e4047;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
